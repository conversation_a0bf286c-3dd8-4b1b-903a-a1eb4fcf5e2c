package com.jky.dgswr.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.dgswr.domain.bo.DgswrReportBo;
import com.jky.dgswr.domain.bo.DgswrWorkfaceReportQueryBo;
import com.jky.dgswr.domain.vo.DgswrReportVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;
import com.jky.dgswr.service.IDgswrReportService;
import com.jky.dgswr.service.IDgswrWorkfaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 工作面信息上报管理
 */
@Validated
@Api(value = "工作面信息上报管理", tags = {"工作面信息上报管理"})
@RestController
@RequestMapping("/swr/workface/report")
@RequiredArgsConstructor
public class DgswrWorkfaceReportController {

    private final IDgswrReportService dgswrReportService;
    private final IDgswrWorkfaceService dgswrWorkfaceService;

    /**
     * 查询工作面上报列表（带分页）
     */
    @ApiOperation("查询工作面上报列表(dgswr-workface-report-list)")
    @GetMapping("/list")
    public Result<IPage<DgswrReportVo>> list(
            DgswrWorkfaceReportQueryBo query,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        
        IPage<DgswrReportVo> page = dgswrReportService.selectWorkfaceReportList(query, pageNo, pageSize);
        return Result.OK("查询成功", page);
    }

    /**
     * 查询工作面上报列表（不分页）
     */
    @ApiOperation("查询工作面上报列表-不分页(dgswr-workface-report-list-all)")
    @GetMapping("/listAll")
    public Result<List<DgswrReportVo>> listAll(DgswrWorkfaceReportQueryBo query) {
        List<DgswrReportVo> list = dgswrReportService.selectWorkfaceReportList(query);
        return Result.OK("查询成功", list);
    }

    /**
     * 查询工作面上报历史记录
     */
    @ApiOperation("查询工作面上报历史记录(dgswr-workface-report-history)")
    @GetMapping("/history/{workfaceId}")
    public Result<List<DgswrReportVo>> getReportHistory(
            @ApiParam(value = "工作面ID", required = true) 
            @PathVariable("workfaceId") String workfaceId) {
        
        // 验证工作面是否存在
        DgswrWorkfaceVo workface = dgswrWorkfaceService.queryById(workfaceId);
        if (workface == null) {
            return Result.error("工作面不存在");
        }
        
        List<DgswrReportVo> historyList = dgswrReportService.selectReportHistoryByWorkfaceId(workfaceId);
        return Result.OK("查询成功", historyList);
    }

    /**
     * 查询工作面最新上报记录
     */
    @ApiOperation("查询工作面最新上报记录(dgswr-workface-report-latest)")
    @GetMapping("/latest/{workfaceId}")
    public Result<DgswrReportVo> getLatestReport(
            @ApiParam(value = "工作面ID", required = true) 
            @PathVariable("workfaceId") String workfaceId) {
        
        // 验证工作面是否存在
        DgswrWorkfaceVo workface = dgswrWorkfaceService.queryById(workfaceId);
        if (workface == null) {
            return Result.error("工作面不存在");
        }
        
        DgswrReportVo latestReport = dgswrReportService.selectLatestReportByWorkfaceId(workfaceId);
        return Result.OK("查询成功", latestReport);
    }

    /**
     * 新增工作面上报记录
     */
    @ApiOperation("新增工作面上报记录(dgswr-workface-report-add)")
    @PostMapping
    public Result<Boolean> add(@Validated @RequestBody DgswrReportBo bo) {
        // 验证工作面是否存在
        DgswrWorkfaceVo workface = dgswrWorkfaceService.queryById(bo.getWorkfaceId());
        if (workface == null) {
            return Result.error("工作面不存在");
        }
        
        Boolean result = dgswrReportService.insert(bo);
        return Result.OK("上报成功", result);
    }

    /**
     * 修改工作面上报记录
     */
    @ApiOperation("修改工作面上报记录(dgswr-workface-report-edit)")
    @PutMapping
    public Result<Boolean> edit(@Validated @RequestBody DgswrReportBo bo) {
        // 验证上报记录是否存在
        DgswrReportVo existingReport = dgswrReportService.queryById(bo.getReportId());
        if (existingReport == null) {
            return Result.error("上报记录不存在");
        }
        
        Boolean result = dgswrReportService.update(bo);
        return Result.OK("修改成功", result);
    }

    /**
     * 查询上报记录详情
     */
    @ApiOperation("查询上报记录详情(dgswr-workface-report-detail)")
    @GetMapping("/{reportId}")
    public Result<DgswrReportVo> getDetail(
            @ApiParam(value = "上报ID", required = true) 
            @PathVariable("reportId") String reportId) {
        
        DgswrReportVo report = dgswrReportService.queryById(reportId);
        if (report == null) {
            return Result.error("上报记录不存在");
        }
        
        return Result.OK("查询成功", report);
    }

    /**
     * 删除工作面上报记录
     */
    @ApiOperation("删除工作面上报记录(dgswr-workface-report-remove)")
    @DeleteMapping("/{reportId}")
    public Result<Boolean> remove(
            @ApiParam(value = "上报ID", required = true) 
            @PathVariable("reportId") String reportId) {
        
        // 验证上报记录是否存在
        DgswrReportVo existingReport = dgswrReportService.queryById(reportId);
        if (existingReport == null) {
            return Result.error("上报记录不存在");
        }
        
        Boolean result = dgswrReportService.deleteById(reportId);
        return Result.OK("删除成功", result);
    }

}
