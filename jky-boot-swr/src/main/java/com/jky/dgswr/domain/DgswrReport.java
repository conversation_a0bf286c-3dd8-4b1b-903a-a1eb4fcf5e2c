package com.jky.dgswr.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 上报表
 */
@Data
@TableName("dgswr_reports")
public class DgswrReport implements Serializable {


    /**
     * 上报ID
     */
    private String reportId;

    /**
     * 工作面ID
     */
    private String workfaceId;

    /**
     * 上报内容
     */
    private String reportContent;

    /**
     * 上报文件
     */
    private String reportFile;

    /**
     * 上报时间
     */
    private Date reportTime;

    /**
     * 上报用户ID
     */
    private String reportUserId;

    /**
     * 上报状态
     */
    private String reportStatus;

    /**
     * 备注
     */
    private String remark;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

}
