package com.jky.dgswr.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作面上报查询条件
 */
@Data
public class DgswrWorkfaceReportQueryBo implements Serializable {

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称（模糊查询）
     */
    private String projectName;

    /**
     * 工作面ID
     */
    private String workfaceId;

    /**
     * 工作面名称（模糊查询）
     */
    private String workfaceName;

    /**
     * 上报状态（0-未上报 1-已上报）
     */
    private String reportStatus;

    /**
     * 上报开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reportStartDate;

    /**
     * 上报结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reportEndDate;

    /**
     * 上报用户ID
     */
    private String reportUserId;

    /**
     * 上报用户名称
     */
    private String reportUserName;

}
