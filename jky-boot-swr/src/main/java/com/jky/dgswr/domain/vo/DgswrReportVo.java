package com.jky.dgswr.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 上报表
 */
@Data
public class DgswrReportVo implements Serializable {


    /**
     * 上报ID
     */
    private String reportId;

    /**
     * 工作面ID
     */
    private String workfaceId;

    /**
     * 上报内容
     */
    private String reportContent;

    /**
     * 上报文件
     */
    private String reportFile;

    /**
     * 上报时间
     */
    private Date reportTime;

    /**
     * 上报用户ID
     */
    private String reportUserId;

    /**
     * 上报状态
     */
    private String reportStatus;

    /**
     * 备注
     */
    private String remark;

}
