package com.jky.dgswr.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.dgswr.domain.DgswrReport;
import com.jky.dgswr.domain.bo.DgswrWorkfaceReportQueryBo;
import com.jky.dgswr.domain.vo.DgswrReportVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 上报管理Mapper接口
 */
public interface DgswrReportMapper extends BaseMapper<DgswrReport> {

    /**
     * 查询工作面上报列表（带分页）
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 工作面上报列表
     */
    IPage<DgswrReportVo> selectWorkfaceReportList(Page<DgswrReportVo> page, @Param("query") DgswrWorkfaceReportQueryBo query);

    /**
     * 查询工作面上报列表（不分页）
     *
     * @param query 查询条件
     * @return 工作面上报列表
     */
    List<DgswrReportVo> selectWorkfaceReportList(@Param("query") DgswrWorkfaceReportQueryBo query);

    /**
     * 根据工作面ID查询上报历史记录
     *
     * @param workfaceId 工作面ID
     * @return 上报历史记录
     */
    List<DgswrReportVo> selectReportHistoryByWorkfaceId(@Param("workfaceId") String workfaceId);

    /**
     * 查询工作面最新上报记录
     *
     * @param workfaceId 工作面ID
     * @return 最新上报记录
     */
    DgswrReportVo selectLatestReportByWorkfaceId(@Param("workfaceId") String workfaceId);

}
