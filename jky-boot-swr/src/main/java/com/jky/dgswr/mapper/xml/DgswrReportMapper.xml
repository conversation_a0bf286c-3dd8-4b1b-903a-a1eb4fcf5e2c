<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.dgswr.mapper.DgswrReportMapper">

    <!-- 工作面上报列表查询 -->
    <select id="selectWorkfaceReportList" resultType="com.jky.dgswr.domain.vo.DgswrReportVo">
        SELECT 
            w.workface_id,
            w.workface_name,
            w.project_id,
            p.project_name,
            r.report_id,
            r.report_content,
            r.report_file,
            r.report_time,
            r.report_user_id,
            u.realname as report_user_name,
            d.depart_name as report_unit,
            r.report_status,
            CASE 
                WHEN r.report_status = '1' THEN '已上报'
                ELSE '未上报'
            END as report_status_text,
            r.remark,
            r.create_time,
            (SELECT MAX(r2.report_time) 
             FROM dgswr_reports r2 
             WHERE r2.workface_id = w.workface_id 
             AND r2.report_status = '1') as latest_report_time
        FROM dgswr_workfaces w
        LEFT JOIN dgswr_project p ON w.project_id = p.project_id
        LEFT JOIN (
            SELECT
                workface_id,
                MAX(report_time) as max_report_time
            FROM dgswr_reports
            WHERE report_status = '1'
            GROUP BY workface_id
        ) latest ON w.workface_id = latest.workface_id
        LEFT JOIN dgswr_reports r ON w.workface_id = r.workface_id
            AND r.report_time = latest.max_report_time
            AND r.report_status = '1'
        LEFT JOIN sys_user u ON r.report_user_id = u.id
        LEFT JOIN sys_depart d ON u.org_code = d.org_code
        <where>
            <if test="query.projectId != null and query.projectId != ''">
                AND w.project_id = #{query.projectId}
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND p.project_name LIKE CONCAT('%', #{query.projectName}, '%')
            </if>
            <if test="query.workfaceId != null and query.workfaceId != ''">
                AND w.workface_id = #{query.workfaceId}
            </if>
            <if test="query.workfaceName != null and query.workfaceName != ''">
                AND w.workface_name LIKE CONCAT('%', #{query.workfaceName}, '%')
            </if>
            <if test="query.reportStatus != null and query.reportStatus != ''">
                <choose>
                    <when test="query.reportStatus == '0'">
                        AND r.report_id IS NULL
                    </when>
                    <when test="query.reportStatus == '1'">
                        AND r.report_status = '1'
                    </when>
                </choose>
            </if>
            <if test="query.reportStartDate != null">
                AND r.report_time >= #{query.reportStartDate}
            </if>
            <if test="query.reportEndDate != null">
                AND r.report_time &lt;= #{query.reportEndDate}
            </if>
            <if test="query.reportUserId != null and query.reportUserId != ''">
                AND r.report_user_id = #{query.reportUserId}
            </if>
            <if test="query.reportUserName != null and query.reportUserName != ''">
                AND u.realname LIKE CONCAT('%', #{query.reportUserName}, '%')
            </if>
        </where>
        ORDER BY 
            CASE WHEN r.report_id IS NULL THEN 0 ELSE 1 END,
            r.report_time DESC,
            w.create_time DESC
    </select>

    <!-- 查询工作面上报历史记录 -->
    <select id="selectReportHistoryByWorkfaceId" resultType="com.jky.dgswr.domain.vo.DgswrReportVo">
        SELECT 
            r.report_id,
            r.workface_id,
            w.workface_name,
            w.project_id,
            p.project_name,
            r.report_content,
            r.report_file,
            r.report_time,
            r.report_user_id,
            u.realname as report_user_name,
            d.depart_name as report_unit,
            r.report_status,
            CASE 
                WHEN r.report_status = '1' THEN '已上报'
                ELSE '未上报'
            END as report_status_text,
            r.remark,
            r.create_time
        FROM dgswr_reports r
        LEFT JOIN dgswr_workfaces w ON r.workface_id = w.workface_id
        LEFT JOIN dgswr_project p ON w.project_id = p.project_id
        LEFT JOIN sys_user u ON r.report_user_id = u.id
        LEFT JOIN sys_depart d ON u.org_code = d.org_code
        WHERE r.workface_id = #{workfaceId}
        ORDER BY r.report_time DESC
    </select>

    <!-- 查询工作面最新上报记录 -->
    <select id="selectLatestReportByWorkfaceId" resultType="com.jky.dgswr.domain.vo.DgswrReportVo">
        SELECT 
            r.report_id,
            r.workface_id,
            w.workface_name,
            w.project_id,
            p.project_name,
            r.report_content,
            r.report_file,
            r.report_time,
            r.report_user_id,
            u.realname as report_user_name,
            d.depart_name as report_unit,
            r.report_status,
            CASE 
                WHEN r.report_status = '1' THEN '已上报'
                ELSE '未上报'
            END as report_status_text,
            r.remark,
            r.create_time
        FROM dgswr_reports r
        LEFT JOIN dgswr_workfaces w ON r.workface_id = w.workface_id
        LEFT JOIN dgswr_project p ON w.project_id = p.project_id
        LEFT JOIN sys_user u ON r.report_user_id = u.id
        LEFT JOIN sys_depart d ON u.org_code = d.org_code
        WHERE r.workface_id = #{workfaceId}
        AND r.report_status = '1'
        ORDER BY r.report_time DESC
        LIMIT 1
    </select>

</mapper>
