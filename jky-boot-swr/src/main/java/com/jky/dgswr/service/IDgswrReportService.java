package com.jky.dgswr.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.dgswr.domain.bo.DgswrReportBo;
import com.jky.dgswr.domain.bo.DgswrWorkfaceReportQueryBo;
import com.jky.dgswr.domain.vo.DgswrReportVo;

import java.util.List;

/**
 * 上报管理Service接口
 */
public interface IDgswrReportService {

    /**
     * 查询工作面上报列表（带分页）
     *
     * @param query    查询条件
     * @param pageNo   页码
     * @param pageSize 每页大小
     * @return 工作面上报列表
     */
    IPage<DgswrReportVo> selectWorkfaceReportList(DgswrWorkfaceReportQueryBo query, Integer pageNo, Integer pageSize);

    /**
     * 查询工作面上报列表（不分页）
     *
     * @param query 查询条件
     * @return 工作面上报列表
     */
    List<DgswrReportVo> selectWorkfaceReportList(DgswrWorkfaceReportQueryBo query);

    /**
     * 根据工作面ID查询上报历史记录
     *
     * @param workfaceId 工作面ID
     * @return 上报历史记录
     */
    List<DgswrReportVo> selectReportHistoryByWorkfaceId(String workfaceId);

    /**
     * 查询工作面最新上报记录
     *
     * @param workfaceId 工作面ID
     * @return 最新上报记录
     */
    DgswrReportVo selectLatestReportByWorkfaceId(String workfaceId);

    /**
     * 新增上报记录
     *
     * @param bo 上报信息
     * @return 是否成功
     */
    Boolean insert(DgswrReportBo bo);

    /**
     * 修改上报记录
     *
     * @param bo 上报信息
     * @return 是否成功
     */
    Boolean update(DgswrReportBo bo);

    /**
     * 根据上报ID查询上报信息
     *
     * @param reportId 上报ID
     * @return 上报信息
     */
    DgswrReportVo queryById(String reportId);

    /**
     * 删除上报记录
     *
     * @param reportId 上报ID
     * @return 是否成功
     */
    Boolean deleteById(String reportId);

}
