package com.jky.dgswr.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.dgswr.domain.DgswrReport;
import com.jky.dgswr.domain.bo.DgswrReportBo;
import com.jky.dgswr.domain.bo.DgswrWorkfaceReportQueryBo;
import com.jky.dgswr.domain.vo.DgswrReportVo;
import com.jky.dgswr.mapper.DgswrReportMapper;
import com.jky.dgswr.service.IDgswrReportService;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.system.util.JwtUtil;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 上报管理Service业务层处理
 */
@Service
@RequiredArgsConstructor
public class DgswrReportServiceImpl extends ServiceImpl<DgswrReportMapper, DgswrReport> implements IDgswrReportService {

    private final DgswrReportMapper dgswrReportMapper;

    @Override
    public IPage<DgswrReportVo> selectWorkfaceReportList(DgswrWorkfaceReportQueryBo query, Integer pageNo, Integer pageSize) {
        Page<DgswrReportVo> page = new Page<>(pageNo, pageSize);
        return dgswrReportMapper.selectWorkfaceReportList(page, query);
    }

    @Override
    public List<DgswrReportVo> selectWorkfaceReportList(DgswrWorkfaceReportQueryBo query) {
        return dgswrReportMapper.selectWorkfaceReportList(query);
    }

    @Override
    public List<DgswrReportVo> selectReportHistoryByWorkfaceId(String workfaceId) {
        return dgswrReportMapper.selectReportHistoryByWorkfaceId(workfaceId);
    }

    @Override
    public DgswrReportVo selectLatestReportByWorkfaceId(String workfaceId) {
        return dgswrReportMapper.selectLatestReportByWorkfaceId(workfaceId);
    }

    @Override
    public Boolean insert(DgswrReportBo bo) {
        DgswrReport report = BeanUtil.copyProperties(bo, DgswrReport.class);
        
        // 设置主键ID
        report.setReportId(IdUtil.getSnowflakeNextIdStr());
        
        // 设置上报时间
        if (report.getReportTime() == null) {
            report.setReportTime(new Date());
        }
        
        // 设置上报用户ID（从当前登录用户获取）
        if (report.getReportUserId() == null) {
            String currentUserId = JwtUtil.getUserNameByToken(JwtUtil.getToken());
            report.setReportUserId(currentUserId);
        }
        
        // 设置上报状态为已上报
        report.setReportStatus("1");
        
        return dgswrReportMapper.insert(report) > 0;
    }

    @Override
    public Boolean update(DgswrReportBo bo) {
        DgswrReport report = BeanUtil.copyProperties(bo, DgswrReport.class);
        return dgswrReportMapper.updateById(report) > 0;
    }

    @Override
    public DgswrReportVo queryById(String reportId) {
        DgswrReport report = dgswrReportMapper.selectById(reportId);
        if (report == null) {
            return null;
        }
        return BeanUtil.copyProperties(report, DgswrReportVo.class);
    }

    @Override
    public Boolean deleteById(String reportId) {
        return dgswrReportMapper.deleteById(reportId) > 0;
    }

}
