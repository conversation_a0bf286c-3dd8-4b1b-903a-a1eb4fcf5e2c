<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.modules.estar.bs.mapper.DataSetTransformMapper">

    <resultMap type="com.jky.modules.estar.bs.entity.DataSetTransform" id="DataSetTransformMap">
        <!--jdbcType="{column.columnType}"-->
        <result property="id" column="id"  />
        <result property="setCode" column="set_code"  />
        <result property="transformType" column="transform_type"  />
        <result property="transformScript" column="transform_script"  />
        <result property="orderNum" column="order_num"  />
        <result property="enableFlag" column="enable_flag"  />
        <result property="deleteFlag" column="delete_flag"  />
        <result property="createBy" column="create_by"  />
        <result property="createTime" column="create_time"  />
        <result property="updateBy" column="update_by"  />
        <result property="updateTime" column="update_time"  />
        <result property="version" column="version"  />

    </resultMap>

    <sql id="Base_Column_List">
        id,set_code,transform_type,transform_script,order_num,enable_flag,delete_flag,create_by,create_time,update_by,update_time,version
    </sql>

    <!--自定义sql -->

</mapper>
