<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.modules.estar.nd.mapper.NdUploadtaskdetailMapper">
  <select id="selectUploadedChunkNumList" parameterType="java.lang.String" resultType="java.lang.Integer">
        select chunkNumber from nd_uploadtaskdetail
        where identifier = #{identifier}
        order by chunkNumber asc
    </select>
</mapper>